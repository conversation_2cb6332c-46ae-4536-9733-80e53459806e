import discord
from discord.ext import commands
from discord import app_commands
import os
import aiohttp
from typing import Optional

# Common language codes and their display names
LANGUAGE_CHOICES = [
    app_commands.Choice(name="Spanish", value="es"),
    app_commands.Choice(name="French", value="fr"),
    app_commands.Choice(name="German", value="de"),
    app_commands.Choice(name="Italian", value="it"),
    app_commands.Choice(name="Portuguese", value="pt"),
    app_commands.Choice(name="Russian", value="ru"),
    app_commands.Choice(name="Japanese", value="ja"),
    app_commands.Choice(name="Korean", value="ko"),
    app_commands.Choice(name="Chinese (Simplified)", value="zh"),
    app_commands.Choice(name="Chinese (Traditional)", value="zh-TW"),
    app_commands.Choice(name="Arabic", value="ar"),
    app_commands.Choice(name="Hindi", value="hi"),
    app_commands.Choice(name="Dutch", value="nl"),
    app_commands.Choice(name="Swedish", value="sv"),
    app_commands.Choice(name="Norwegian", value="no"),
    app_commands.Choice(name="Danish", value="da"),
    app_commands.Choice(name="Finnish", value="fi"),
    app_commands.Choice(name="Polish", value="pl"),
    app_commands.Choice(name="Czech", value="cs"),
    app_commands.Choice(name="Hungarian", value="hu"),
    app_commands.Choice(name="Turkish", value="tr"),
    app_commands.Choice(name="Greek", value="el"),
    app_commands.Choice(name="Hebrew", value="he"),
    app_commands.Choice(name="Thai", value="th"),
    app_commands.Choice(name="Vietnamese", value="vi"),
    app_commands.Choice(name="English", value="en"),
]

async def _translate_text_ai(text: str, target_language: str, source_language: Optional[str] = None) -> str:
    """Translate text using AI via OpenRouter."""
    api_key = os.getenv("AI_API_KEY")
    if not api_key:
        raise RuntimeError("AI_API_KEY environment variable not set.")
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}
    
    # Create the translation prompt
    if source_language:
        system_prompt = (
            f"You are a professional translator. Translate the following text from {source_language} to {target_language}. "
            "Provide ONLY the translated text without any additional commentary, explanations, or formatting. "
            "Preserve the original tone, style, and meaning as much as possible. "
            "If the text is already in the target language, return it unchanged."
        )
    else:
        system_prompt = (
            f"You are a professional translator. Translate the following text to {target_language}. "
            "Automatically detect the source language. "
            "Provide ONLY the translated text without any additional commentary, explanations, or formatting. "
            "Preserve the original tone, style, and meaning as much as possible. "
            "If the text is already in the target language, return it unchanged."
        )
    
    payload = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text}
        ],
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as resp:
            if resp.content_type == "application/json":
                data = await resp.json()
                return data["choices"][0]["message"]["content"]
            else:
                text = await resp.text()
                raise RuntimeError(
                    f"OpenRouter API returned non-JSON response (status {resp.status}): {text[:500]}"
                )


class TranslationCog(commands.Cog):
    def __init__(self, bot: commands.Bot):
        self.bot = bot

    @app_commands.command(name="translate", description="Translate text to another language using AI")
    @app_commands.describe(
        text="The text to translate",
        target_language="The language to translate to",
        source_language="The source language (optional, will auto-detect if not specified)"
    )
    @app_commands.choices(target_language=LANGUAGE_CHOICES)
    @app_commands.choices(source_language=LANGUAGE_CHOICES)
    async def translate_slash_command(
        self, 
        interaction: discord.Interaction, 
        text: str, 
        target_language: str,
        source_language: Optional[str] = None
    ):
        """Translates the provided text via a slash command."""
        if not text.strip():
            await interaction.response.send_message(
                "You need to provide some text to translate!", ephemeral=True
            )
            return
        
        # Get language display name for target
        target_display = next((choice.name for choice in LANGUAGE_CHOICES if choice.value == target_language), target_language)
        source_display = next((choice.name for choice in LANGUAGE_CHOICES if choice.value == source_language), source_language) if source_language else "Auto-detect"
        
        try:
            await interaction.response.defer()
            translated_text = await _translate_text_ai(text, target_display, source_display if source_language else None)
            
            # Create an embed for better formatting
            embed = discord.Embed(
                title="Translation",
                color=0x00ff00
            )
            embed.add_field(name=f"Original ({source_display})", value=text[:1024], inline=False)
            embed.add_field(name=f"Translated ({target_display})", value=translated_text[:1024], inline=False)
            embed.set_footer(text=f"Translated by {interaction.user.display_name}")
            
            await interaction.followup.send(embed=embed)
        except Exception as e:
            await interaction.followup.send(
                f"Translation failed: {e}", ephemeral=True
            )


# Context menu command must be defined at module level
@app_commands.context_menu(name="Translate Message")
async def translate_context_menu(
    interaction: discord.Interaction, message: discord.Message
):
    """Translates the content of the selected message to English."""
    if not message.content:
        await interaction.response.send_message(
            "The selected message has no text content to translate!", ephemeral=True
        )
        return
    
    original_content = message.content
    translated_text = ""
    try:
        await interaction.response.defer()
        translated_text = await _translate_text_ai(original_content, "English")

        # Create an embed for the translation
        embed = discord.Embed(
            title="Message Translation",
            color=0x00ff00
        )
        embed.add_field(name="Original", value=original_content[:1024], inline=False)
        embed.add_field(name="Translated (English)", value=translated_text[:1024], inline=False)
        embed.set_footer(text=f"Translated by {interaction.user.display_name}")

        # Reply to the original message with the translation
        await message.reply(embed=embed)
        await interaction.followup.send(
            "Message translated and replied!", ephemeral=True
        )
    except discord.Forbidden:
        if translated_text:
            await interaction.followup.send(
                f"I couldn't reply to the message (no permissions?).\n"
                f"But here's the translation: {translated_text}",
                ephemeral=True,
            )
        else:
            await interaction.followup.send(
                "I couldn't reply to the message and translation failed.", ephemeral=True
            )
    except Exception as e:
        await interaction.followup.send(
            f"Translation failed: {e}", ephemeral=True
        )


async def setup(bot: commands.Bot):
    cog = TranslationCog(bot)
    await bot.add_cog(cog)
    bot.tree.add_command(translate_context_menu)
    print("TranslationCog loaded!")
